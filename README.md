# DuckDB Streaming JSON Reader Extension

A memory-efficient streaming JSON reader extension for DuckDB implemented in Rust. This extension uses streaming JSON parsing to avoid loading entire JSON files into memory, enabling processing of large JSON files that exceed available RAM.

## Features

- **Memory Efficient**: Uses streaming parsing - only loads individual JSON objects, not entire files
- **Recursive JSON Support**: Handles deeply nested JSON structures with automatic flattening
- **Projection Pushdown**: Only parses JSON fields that are actually requested in queries
- **Path-based Column Naming**: Generates unique column names from JSON paths to avoid conflicts
- **DuckDB Integration**: Seamless integration with DuckDB's query planner and type system

## Quick Start

### Build the Extension

```shell
make configure
make debug
```

### Load and Use

```python
import duckdb
conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
conn.execute('LOAD "build/debug/streaming_json_reader.duckdb_extension"')

# Query JSON file
result = conn.execute('SELECT * FROM streaming_json_reader("path/to/file.json")').fetchall()
```

### Basic Usage Examples

```sql
-- Select all columns from JSON file
SELECT * FROM streaming_json_reader('data.json');

-- Select specific nested fields
SELECT metadata_name, users_name, users_age 
FROM streaming_json_reader('nested.json');

-- Use with WHERE clauses and aggregations
SELECT users_department, COUNT(*) 
FROM streaming_json_reader('users.json') 
WHERE users_age > 25 
GROUP BY users_department;
```

## JSON Structure Support

The extension automatically handles complex JSON structures:

### Nested Objects
```json
{
  "metadata": {"name": "Dataset", "version": "1.0"},
  "users": [
    {"id": 1, "name": "Alice", "profile": {"age": 30, "city": "NYC"}},
    {"id": 2, "name": "Bob", "profile": {"age": 25, "city": "LA"}}
  ]
}
```

Generates columns: `metadata_name`, `metadata_version`, `users_id`, `users_name`, `users_profile_age`, `users_profile_city`

### Array Flattening
Arrays are automatically flattened into rows. Each array element becomes a separate row with parent context preserved.

## Column Naming Strategy

- **Path-based naming**: `users[*].profile.age` → `users_profile_age`
- **Conflict resolution**: Different paths create unique column names
- **Root arrays**: `[*].name` → `name` (no prefix needed)

## Dependencies

- Rust toolchain
- Python3 and Python3-venv
- [Make](https://www.gnu.org/software/make)
- Git

## Building

```shell
# Configure build environment
make configure

# Build debug version
make debug

# Build release version  
make release
```

The build process creates `build/debug/streaming_json_reader.duckdb_extension` which can be loaded into DuckDB.

## Testing

Run the test suite:

```shell
# Run debug tests
make test_debug

# Run release tests  
make test_release
```

### Manual Testing

```python
import duckdb
conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
conn.execute('LOAD "build/debug/streaming_json_reader.duckdb_extension"')

# Test with sample data
conn.execute('SELECT * FROM streaming_json_reader("tests/test_multiply_nested.json")').fetchall()
```

## Current Status

### ✅ Implemented
- Recursive JSON schema discovery
- Path-based column generation  
- DuckDB table function integration
- Projection pushdown support
- Basic streaming infrastructure

### 🚧 Critical Issues (In Progress)
1. **Column Name Conflicts**: Multiple JSON paths creating duplicate column names
2. **Number Type Display**: Scientific notation instead of clean integers/floats  
3. **Streaming Implementation**: Not yet using generated paths for efficient parsing
4. **Memory Optimization**: Not achieving memory efficiency goals

### 📋 Next Steps
1. Fix column naming conflicts with proper path-based prefixing
2. Implement true streaming that only parses projected JSON segments
3. Add memory usage comparison tests vs default DuckDB JSON reader
4. Performance optimization and benchmarking

## Architecture

See [design_decisions.md](design_decisions.md) for detailed architectural decisions and implementation approaches.
