extern crate duckdb;
extern crate duckdb_loadable_macros;
extern crate libduckdb_sys;

use duckdb::{
    core::{DataChunkHandle, Inserter, LogicalTypeHandle, LogicalTypeId},
    vtab::{BindInfo, InitInfo, TableFunctionInfo, VTab},
    Connection, Result,
};
use duckdb_loadable_macros::duckdb_entrypoint_c_api;
use libduckdb_sys as ffi;
use std::{
    error::Error,
    ffi::CString,
    fs::File,
    io::BufReader,
    path::Path,
    sync::atomic::{AtomicBool, AtomicUsize, Ordering},
};
use struson::reader::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>sonStreamReader};

/// Represents a column index with potential nested field access
#[derive(Debug, Clone)]
struct ColumnIndex {
    index: usize,
    child_indexes: Vec<ColumnIndex>,
}

impl ColumnIndex {
    fn new(index: usize) -> Self {
        Self {
            index,
            child_indexes: Vec::new(),
        }
    }

    fn with_children(index: usize, children: Vec<ColumnIndex>) -> Self {
        Self {
            index,
            child_indexes: children,
        }
    }

    fn has_children(&self) -> bool {
        !self.child_indexes.is_empty()
    }

    fn get_primary_index(&self) -> usize {
        self.index
    }

    fn get_child_indexes(&self) -> &[ColumnIndex] {
        &self.child_indexes
    }
}

/// Represents a JSON value type with proper recursive semantics
#[derive(Debug, Clone, PartialEq)]
enum JsonValueType {
    Null,
    Boolean,
    Number,
    String,
    Array(Box<JsonValueType>),      // Array of elements of this type
    Object(Vec<JsonField>),         // Object with named fields
}

/// Represents a field in a JSON object
#[derive(Debug, Clone, PartialEq)]
struct JsonField {
    name: String,
    value_type: JsonValueType,
}

/// Represents a path through the JSON structure
#[derive(Debug, Clone, PartialEq)]
enum JsonPathSegment {
    Field(String),                  // Access object field by name
    ArrayIndex(usize),              // Access specific array index
    ArrayWildcard,                  // Flatten array into rows
}

/// Complete path to a value in JSON structure
#[derive(Debug, Clone)]
struct JsonPath {
    segments: Vec<JsonPathSegment>,
}

impl JsonPath {
    fn root() -> Self {
        Self { segments: vec![] }
    }

    fn field(name: &str) -> Self {
        Self { segments: vec![JsonPathSegment::Field(name.to_string())] }
    }

    fn extend_field(&self, name: &str) -> Self {
        let mut segments = self.segments.clone();
        segments.push(JsonPathSegment::Field(name.to_string()));
        Self { segments }
    }

    fn extend_array_wildcard(&self) -> Self {
        let mut segments = self.segments.clone();
        segments.push(JsonPathSegment::ArrayWildcard);
        Self { segments }
    }

    fn to_string(&self) -> String {
        let mut result = String::new();
        for (i, segment) in self.segments.iter().enumerate() {
            if i > 0 { result.push('.'); }
            match segment {
                JsonPathSegment::Field(name) => result.push_str(name),
                JsonPathSegment::ArrayIndex(idx) => result.push_str(&format!("[{}]", idx)),
                JsonPathSegment::ArrayWildcard => result.push_str("[*]"),
            }
        }
        result
    }
}

/// Represents the discovered JSON schema with recursive structure
#[derive(Debug, Clone)]
struct JsonSchema {
    root_type: JsonValueType,
    columns: Vec<StructuredColumn>,
}

/// Represents a column with proper structured types (STRUCT/ARRAY)
#[derive(Debug, Clone)]
struct StructuredColumn {
    name: String,
    value_type: JsonValueType,
}

/// Query projection information mapped to JSON paths
#[derive(Debug, Clone)]
struct JsonProjection {
    required_paths: Vec<JsonPath>,
    column_mapping: Vec<(usize, JsonPath)>, // (column_index, json_path)
}

#[repr(C)]
struct JsonReaderBindData {
    file_path: String,
    schema: JsonSchema,
}

#[repr(C)]
struct JsonReaderInitData {
    current_element: AtomicUsize,
    finished: AtomicBool,
    projected_columns: Vec<ColumnIndex>, // Columns requested by the query
}

struct JsonReaderVTab;

// Helper function to discover JSON schema with proper recursive analysis
fn discover_json_schema(
    file_path: &str,
    projected_columns: Option<&[ColumnIndex]>
) -> Result<JsonSchema, Box<dyn std::error::Error>> {
    // Check if file exists
    if !Path::new(file_path).exists() {
        return Err(format!("File does not exist: {}", file_path).into());
    }

    // Open file and create JSON reader
    let file = File::open(file_path)?;
    let buf_reader = BufReader::new(file);
    let mut json_reader = JsonStreamReader::new(buf_reader);

    // Analyze the root JSON structure recursively
    let root_type = analyze_json_value(&mut json_reader, &JsonPath::root())?;

    // Generate structured columns based on the discovered structure
    let columns = if let Some(projected) = projected_columns {
        // Query-driven: only generate columns for projected fields
        generate_projected_columns(&root_type, projected)?
    } else {
        // Discovery mode: generate all possible columns
        generate_all_columns(&root_type)?
    };

    if columns.is_empty() {
        return Err("No suitable JSON structure found for table representation".into());
    }

    Ok(JsonSchema {
        root_type,
        columns,
    })
}

// Recursively analyze JSON structure to build proper type representation
fn analyze_json_value(
    json_reader: &mut JsonStreamReader<BufReader<File>>,
    _current_path: &JsonPath
) -> Result<JsonValueType, Box<dyn std::error::Error>> {
    match json_reader.peek()? {
        struson::reader::ValueType::Null => {
            json_reader.next_null()?;
            Ok(JsonValueType::Null)
        }
        struson::reader::ValueType::Boolean => {
            json_reader.next_bool()?;
            Ok(JsonValueType::Boolean)
        }
        struson::reader::ValueType::Number => {
            json_reader.next_number_as_str()?;
            Ok(JsonValueType::Number)
        }
        struson::reader::ValueType::String => {
            json_reader.next_string()?;
            Ok(JsonValueType::String)
        }
        struson::reader::ValueType::Array => {
            json_reader.begin_array()?;

            // Analyze first element to determine array element type
            let element_type = if json_reader.has_next()? {
                let first_element_type = analyze_json_value(json_reader, _current_path)?;

                // Skip remaining elements for now (we could analyze more for union types)
                while json_reader.has_next()? {
                    json_reader.skip_value()?;
                }

                first_element_type
            } else {
                // Empty array - assume string elements
                JsonValueType::String
            };

            json_reader.end_array()?;
            Ok(JsonValueType::Array(Box::new(element_type)))
        }
        struson::reader::ValueType::Object => {
            json_reader.begin_object()?;
            let mut fields = Vec::new();

            while json_reader.has_next()? {
                let field_name = json_reader.next_name()?.to_string();
                let field_path = _current_path.extend_field(&field_name);
                let field_type = analyze_json_value(json_reader, &field_path)?;

                fields.push(JsonField {
                    name: field_name,
                    value_type: field_type,
                });
            }

            json_reader.end_object()?;
            Ok(JsonValueType::Object(fields))
        }
    }
}

// Generate columns for projected fields only
fn generate_projected_columns(
    _root_type: &JsonValueType,
    _projected: &[ColumnIndex]
) -> Result<Vec<StructuredColumn>, Box<dyn std::error::Error>> {
    // TODO: Implement query-driven column generation
    Err("Query-driven column generation not yet implemented".into())
}

// Generate structured columns from JSON structure (preserving hierarchy)
fn generate_all_columns(
    root_type: &JsonValueType
) -> Result<Vec<StructuredColumn>, Box<dyn std::error::Error>> {
    match root_type {
        JsonValueType::Object(fields) => {
            // Root object: each field becomes a top-level column
            let mut columns = Vec::new();
            for field in fields {
                columns.push(StructuredColumn {
                    name: field.name.clone(),
                    value_type: field.value_type.clone(),
                });
            }
            Ok(columns)
        }
        JsonValueType::Array(element_type) => {
            // Root array: create a single column containing the array
            Ok(vec![StructuredColumn {
                name: "value".to_string(),
                value_type: root_type.clone(),
            }])
        }
        _ => {
            // Root primitive: single column
            Ok(vec![StructuredColumn {
                name: "value".to_string(),
                value_type: root_type.clone(),
            }])
        }
    }
}

// Convert JsonValueType to DuckDB LogicalTypeHandle
fn json_type_to_duckdb_type(json_type: &JsonValueType) -> Result<LogicalTypeHandle, Box<dyn std::error::Error>> {
    match json_type {
        JsonValueType::Boolean => Ok(LogicalTypeHandle::from(LogicalTypeId::Boolean)),
        JsonValueType::Number => Ok(LogicalTypeHandle::from(LogicalTypeId::Double)),
        JsonValueType::String => Ok(LogicalTypeHandle::from(LogicalTypeId::Varchar)),
        JsonValueType::Null => Ok(LogicalTypeHandle::from(LogicalTypeId::Varchar)),
        JsonValueType::Array(element_type) => {
            // Create LIST type
            let element_logical_type = json_type_to_duckdb_type(element_type)?;
            Ok(LogicalTypeHandle::list(&element_logical_type))
        }
        JsonValueType::Object(fields) => {
            // Create STRUCT type
            let mut struct_fields = Vec::new();

            for field in fields {
                let field_type = json_type_to_duckdb_type(&field.value_type)?;
                struct_fields.push((field.name.as_str(), field_type));
            }

            Ok(LogicalTypeHandle::struct_type(&struct_fields))
        }
    }
}

// Read JSON file and convert to structured data for DuckDB insertion
fn read_json_as_structured_data(
    file_path: &str,
    schema: &JsonSchema,
) -> Result<Vec<Vec<serde_json::Value>>, Box<dyn std::error::Error>> {
    // Read the entire JSON file
    let file_content = std::fs::read_to_string(file_path)?;
    let json_value: serde_json::Value = serde_json::from_str(&file_content)?;

    // Convert JSON to row format based on schema
    let mut rows = Vec::new();

    match &json_value {
        serde_json::Value::Object(_) => {
            // Single object - create one row with values for each column
            let mut row = Vec::new();
            for column in &schema.columns {
                if let Some(field_value) = json_value.get(&column.name) {
                    row.push(field_value.clone());
                } else {
                    row.push(serde_json::Value::Null);
                }
            }
            rows.push(row);
        }
        serde_json::Value::Array(arr) => {
            // Array of objects - each object becomes a row
            for item in arr {
                if let serde_json::Value::Object(_) = item {
                    let mut row = Vec::new();
                    for column in &schema.columns {
                        if let Some(field_value) = item.get(&column.name) {
                            row.push(field_value.clone());
                        } else {
                            row.push(serde_json::Value::Null);
                        }
                    }
                    rows.push(row);
                }
            }
        }
        _ => {
            return Err("Expected JSON object or array at root".into());
        }
    }

    Ok(rows)
}

// Insert a structured value into a DuckDB vector
fn insert_structured_value(
    output: &DataChunkHandle,
    col_idx: usize,
    row_idx: usize,
    value: &serde_json::Value,
) -> Result<(), Box<dyn std::error::Error>> {
    // For now, convert everything to string representation
    // TODO: Implement proper structured data insertion for STRUCT/ARRAY types
    let vector = output.flat_vector(col_idx);
    let value_str = match value {
        serde_json::Value::String(s) => s.clone(),
        serde_json::Value::Number(n) => n.to_string(),
        serde_json::Value::Bool(b) => b.to_string(),
        serde_json::Value::Null => "null".to_string(),
        _ => serde_json::to_string(value)?,
    };

    let cstring = std::ffi::CString::new(value_str)?;
    vector.insert(row_idx, cstring);
    Ok(())
}

// Helper function to read and flatten JSON arrays generically
fn read_and_flatten_json(
    file_path: &str,
    schema: &JsonSchema,
    init_data: &JsonReaderInitData
) -> Result<Vec<Vec<String>>, Box<dyn std::error::Error>> {
    // Check if file exists
    if !Path::new(file_path).exists() {
        return Err(format!("File does not exist: {}", file_path).into());
    }

    // For the first call, read the entire array and return first batch
    let current_element = init_data.current_element.load(Ordering::Relaxed);

    if current_element > 0 {
        // We've already processed some elements, return empty to indicate we're done
        // TODO: Implement proper streaming across multiple calls
        return Ok(vec![]);
    }

    // Open file and create JSON reader
    let file = File::open(file_path)?;
    let buf_reader = BufReader::new(file);
    let mut json_reader = JsonStreamReader::new(buf_reader);

    let columns = &schema.columns;
    let mut result_columns: Vec<Vec<String>> = vec![Vec::new(); columns.len()];
    let mut elements_read = 0;
    let max_elements = 100; // Limit to prevent memory issues

    match json_reader.peek()? {
        struson::reader::ValueType::Object => {
            json_reader.begin_object()?;

            // Look for the first array field
            while json_reader.has_next()? {
                let _field_name = json_reader.next_name()?;

                if let struson::reader::ValueType::Array = json_reader.peek()? {
                    json_reader.begin_array()?;

                    // Process array elements
                    while json_reader.has_next()? && elements_read < max_elements {
                        if let struson::reader::ValueType::Object = json_reader.peek()? {
                            json_reader.begin_object()?;
                            let mut row_data = vec!["".to_string(); columns.len()];

                            while json_reader.has_next()? {
                                let field_name = json_reader.next_name()?;
                                if let Some(col_idx) = columns.iter().position(|c| c.name == field_name) {
                                    // Extract the value for this column
                                    let value = match json_reader.peek()? {
                                        struson::reader::ValueType::String => json_reader.next_string()?,
                                        struson::reader::ValueType::Number => json_reader.next_number_as_str()?.to_string(),
                                        struson::reader::ValueType::Boolean => json_reader.next_bool()?.to_string(),
                                        struson::reader::ValueType::Null => {
                                            json_reader.next_null()?;
                                            "null".to_string()
                                        }
                                        _ => {
                                            json_reader.skip_value()?;
                                            "".to_string()
                                        }
                                    };
                                    row_data[col_idx] = value;
                                } else {
                                    json_reader.skip_value()?;
                                }
                            }
                            json_reader.end_object()?;

                            // Add row data to result columns
                            for (col_idx, value) in row_data.into_iter().enumerate() {
                                result_columns[col_idx].push(value);
                            }
                            elements_read += 1;
                        } else {
                            json_reader.skip_value()?;
                            elements_read += 1;
                        }
                    }

                    break; // Found our array, stop looking
                } else {
                    json_reader.skip_value()?;
                }
            }
        }
        struson::reader::ValueType::Array => {
            // Direct array at root level
            json_reader.begin_array()?;

            while json_reader.has_next()? && elements_read < max_elements {
                if let struson::reader::ValueType::Object = json_reader.peek()? {
                    json_reader.begin_object()?;
                    let mut row_data = vec!["".to_string(); columns.len()];

                    while json_reader.has_next()? {
                        let field_name = json_reader.next_name()?;
                        if let Some(col_idx) = columns.iter().position(|c| c.name == field_name) {
                            // Extract the value for this column
                            let value = match json_reader.peek()? {
                                struson::reader::ValueType::String => json_reader.next_string()?,
                                struson::reader::ValueType::Number => json_reader.next_number_as_str()?.to_string(),
                                struson::reader::ValueType::Boolean => json_reader.next_bool()?.to_string(),
                                struson::reader::ValueType::Null => {
                                    json_reader.next_null()?;
                                    "null".to_string()
                                }
                                _ => {
                                    json_reader.skip_value()?;
                                    "".to_string()
                                }
                            };
                            row_data[col_idx] = value;
                        } else {
                            json_reader.skip_value()?;
                        }
                    }
                    json_reader.end_object()?;

                    // Add row data to result columns
                    for (col_idx, value) in row_data.into_iter().enumerate() {
                        result_columns[col_idx].push(value);
                    }
                    elements_read += 1;
                } else {
                    json_reader.skip_value()?;
                    elements_read += 1;
                }
            }
        }
        _ => {
            return Err("Expected JSON object or array at root".into());
        }
    }

    // Mark as finished since we read everything in one go
    init_data.finished.store(true, Ordering::Relaxed);
    init_data.current_element.store(elements_read, Ordering::Relaxed);

    Ok(result_columns)
}





impl VTab for JsonReaderVTab {
    type InitData = JsonReaderInitData;
    type BindData = JsonReaderBindData;

    fn bind(bind: &BindInfo) -> Result<Self::BindData, Box<dyn std::error::Error>> {
        // Get the file path parameter
        let file_path = bind.get_parameter(0).to_string();

        eprintln!("DEBUG BIND: File path: {}", file_path);
        eprintln!("DEBUG BIND: Parameter count: {}", bind.get_parameter_count());

        // Try to explore what other information might be available in bind phase
        eprintln!("DEBUG BIND: Exploring BindInfo for additional projection information...");

        // Let's see if BindInfo has any methods that might give us nested field information
        // This is exploratory to understand what's available
        eprintln!("DEBUG BIND: Checking for nested field projection capabilities...");

        // Discover JSON schema from the file (no projection info available at bind time)
        let schema = match discover_json_schema(&file_path, None) {
            Ok(schema) => {
                eprintln!("DEBUG BIND: Discovered schema: {:?}", schema);
                schema
            },
            Err(e) => {
                eprintln!("DEBUG BIND: Schema discovery failed: {}", e);
                // Fallback to generic schema if discovery fails
                JsonSchema {
                    root_type: JsonValueType::Object(vec![
                        JsonField { name: "id".to_string(), value_type: JsonValueType::String },
                        JsonField { name: "name".to_string(), value_type: JsonValueType::String },
                        JsonField { name: "value".to_string(), value_type: JsonValueType::String },
                    ]),
                    columns: vec![
                        StructuredColumn { name: "id".to_string(), value_type: JsonValueType::String },
                        StructuredColumn { name: "name".to_string(), value_type: JsonValueType::String },
                        StructuredColumn { name: "value".to_string(), value_type: JsonValueType::String },
                    ],
                }
            }
        };

        // Add result columns to DuckDB based on structured schema
        for (i, column) in schema.columns.iter().enumerate() {
            eprintln!("DEBUG BIND: Adding column '{}' at index {}", column.name, i);

            // Convert JSON types to DuckDB logical types (including STRUCT/ARRAY)
            let logical_type = json_type_to_duckdb_type(&column.value_type)?;

            bind.add_result_column(&column.name, logical_type);
            eprintln!("DEBUG BIND: Added '{}' as {:?} type", column.name, column.value_type);
        }

        Ok(JsonReaderBindData {
            file_path,
            schema,
        })
    }

    fn init(init: &InitInfo) -> Result<Self::InitData, Box<dyn std::error::Error>> {
        // Test projection pushdown capabilities
        eprintln!("DEBUG: Testing projection pushdown capabilities");

        // Get column indices for projection pushdown
        let column_indices = init.get_column_indices();
        eprintln!("DEBUG: Projected column indices: {:?}", column_indices);
        eprintln!("DEBUG: Number of projected columns: {}", column_indices.len());

        // Get bind data to access column names
        let bind_data = unsafe { &*(init.get_bind_data() as *const JsonReaderBindData) };
        let column_names: Vec<String> = bind_data.schema.columns.iter().map(|c| c.name.clone()).collect();
        eprintln!("DEBUG: All available columns: {:?}", column_names);

        // Map indices to actual column names
        let projected_column_names: Vec<String> = column_indices
            .iter()
            .map(|&idx| {
                if (idx as usize) < column_names.len() {
                    column_names[idx as usize].clone()
                } else {
                    format!("UNKNOWN_COLUMN_{}", idx)
                }
            })
            .collect();

        eprintln!("DEBUG: Projected column names: {:?}", projected_column_names);

        // Try to explore what other methods might be available on InitInfo
        eprintln!("DEBUG: Exploring InitInfo methods...");

        // Let's try to see if there are any other methods we can call on InitInfo
        // This is exploratory - we'll see what's available

        // Check if there's any way to get more detailed projection information
        eprintln!("DEBUG: Checking for additional projection information...");

        // Let's see if there are any other methods we can call
        // (This is exploratory - some might not exist)

        if column_indices.is_empty() {
            eprintln!("DEBUG: No specific columns projected (SELECT * query?)");
        } else {
            eprintln!("DEBUG: Specific columns requested: {:?}", projected_column_names);
        }

        // Convert column indices to our ColumnIndex structure
        let projected_columns: Vec<ColumnIndex> = column_indices
            .iter()
            .map(|&idx| ColumnIndex::new(idx as usize))
            .collect();

        Ok(JsonReaderInitData {
            current_element: AtomicUsize::new(0),
            finished: AtomicBool::new(false),
            projected_columns,
        })
    }

    fn func(func: &TableFunctionInfo<Self>, output: &mut DataChunkHandle) -> Result<(), Box<dyn std::error::Error>> {
        let init_data = func.get_init_data();
        let bind_data = unsafe { &*(func.get_bind_data() as *const JsonReaderBindData) };

        if init_data.finished.load(Ordering::Relaxed) {
            output.set_len(0);
            return Ok(());
        }

        eprintln!("DEBUG FUNC: Processing file: {}", bind_data.file_path);
        let column_names: Vec<String> = bind_data.schema.columns.iter().map(|c| c.name.clone()).collect();
        eprintln!("DEBUG FUNC: Available columns: {:?}", column_names);

        // Read JSON file and create structured data
        eprintln!("DEBUG FUNC: Reading JSON file for structured data insertion");

        match read_json_as_structured_data(&bind_data.file_path, &bind_data.schema) {
            Ok(rows) => {
                eprintln!("DEBUG FUNC: Successfully read {} rows", rows.len());

                if rows.is_empty() {
                    output.set_len(0);
                } else {
                    // Insert structured data into DuckDB vectors
                    for (row_idx, row_data) in rows.iter().enumerate() {
                        for (col_idx, value) in row_data.iter().enumerate() {
                            if col_idx < bind_data.schema.columns.len() {
                                insert_structured_value(output, col_idx, row_idx, value)?;
                            }
                        }
                    }
                    output.set_len(rows.len());
                    eprintln!("DEBUG FUNC: Inserted {} rows with structured data", rows.len());
                }
            }
            Err(e) => {
                eprintln!("DEBUG FUNC: Error reading structured JSON: {}", e);
                output.set_len(0);
            }
        }

        Ok(())
    }

    fn parameters() -> Option<Vec<LogicalTypeHandle>> {
        Some(vec![LogicalTypeHandle::from(LogicalTypeId::Varchar)])
    }
}

#[duckdb_entrypoint_c_api()]
pub unsafe fn extension_entrypoint(con: Connection) -> Result<(), Box<dyn Error>> {
    con.register_table_function::<JsonReaderVTab>("streaming_json_reader")
        .expect("Failed to register streaming JSON reader table function");
    Ok(())
}